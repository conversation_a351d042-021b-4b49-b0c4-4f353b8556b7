import { Document, Types } from 'mongoose';

export interface IContractModel extends Document {
    _id: Types.ObjectId;
    itemId: Types.ObjectId;
     contractTypeId: Types.ObjectId;
     price: number;
     quantity: number;
     paymentTerms: string;
     preventiveMaintenance: string;
     startDate: Date;
     endDate: Date;
     status: string,
     createdById: Types.ObjectId;
     updatedById: Types.ObjectId
  
}
