import * as express from 'express';
import Item from '../controllers/itemController'
import ApiResponse from '../helpers/response'

class ItemRoute {
  public path = '/'
  public router = express.Router()
  private itemController: Item
  constructor() {
    this.initRoutes()
    this.itemController = new Item()
  }

  public initRoutes() {

    this.router.route('/item')
      .post(async (req, res) => {
        const resBody: ApiResponse = await this.itemController.createItem(req)
        return res.status(resBody.code).send(resBody)
      })
      .get(async (req, res) => {
        const resBody: ApiResponse = await this.itemController.fetchItem(req)
        return res.status(resBody.code).send(resBody)
      })

    this.router.route('/item/:itemId')
      .get(async (req, res) => {
        const resBody: ApiResponse = await this.itemController.fetchItemById(req)
        return res.status(resBody.code).send(resBody)
      })
      .put(async (req, res) => {
        const resBody: ApiResponse = await this.itemController.updateItemById(req)
        return res.status(resBody.code).send(resBody)
      })

  
  }
}

export default ItemRoute
