import * as express from 'express'

class DefaultRoute {
  public router = express.Router()
  constructor() {
    this.initRoutes()
  }

  public initRoutes() {
    this.router.route('/')
      .get(async (req, res) => {
        return res.status(200).send({
          "status": true,
          "message": "Hurray! product service is running successfully"
        })
      })
  }
}
export default DefaultRoute
