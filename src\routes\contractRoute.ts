import * as express from 'express';
import Contract from '../controllers/contractController'
import ApiResponse from '../helpers/response'

class ContractRoute {
  public path = '/'
  public router = express.Router()
  private contractController: Contract
  constructor() {
    this.initRoutes()
    this.contractController = new Contract()
  }

  public initRoutes() {

    this.router.route('/contract')
      .post(async (req, res) => {
        const resBody: ApiResponse = await this.contractController.createContract(req)
        return res.status(resBody.code).send(resBody)
      })
      .get(async (req, res) => {
        const resBody: ApiResponse = await this.contractController.fetchContract(req)
        return res.status(resBody.code).send(resBody)
      })

    this.router.route('/contract/:contractId')
      .get(async (req, res) => {
        const resBody: ApiResponse = await this.contractController.fetchContractById(req)
        return res.status(resBody.code).send(resBody)
      })
      .put(async (req, res) => {
        const resBody: ApiResponse = await this.contractController.updateContractById(req)
        return res.status(resBody.code).send(resBody)
      })

  
  }
}

export default ContractRoute
