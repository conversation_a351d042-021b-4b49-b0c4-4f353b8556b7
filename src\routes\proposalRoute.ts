import * as express from 'express';
import Proposal from '../controllers/proposalController'
import ApiResponse from '../helpers/response'
import * as multer from 'multer';

class ProposalRoute {
  public path = '/'
  public router = express.Router()
  private proposalController: Proposal
  private upload = multer();
  
  constructor() {
    this.initRoutes()
    this.proposalController = new Proposal()
  }

  public initRoutes() {

    this.router.route('/proposal')
      .post(this.upload.array('attachments'),async (req, res) => {
        const resBody: ApiResponse = await this.proposalController.createProposal(req)
        return res.status(resBody.code).send(resBody)
      })
      .get(async (req, res) => {
        const resBody: ApiResponse = await this.proposalController.fetchProposal(req)
        return res.status(resBody.code).send(resBody)
      })

    this.router.route('/proposal/:proposalId')
      .get(async (req, res) => {
        const resBody: ApiResponse = await this.proposalController.fetchProposalById(req)
        return res.status(resBody.code).send(resBody)
      })
      .put(this.upload.array('attachments'),async (req, res) => {
        const resBody: ApiResponse = await this.proposalController.updateProposalById(req)
        return res.status(resBody.code).send(resBody)
      })

  
  }
}

export default ProposalRoute
