import { contractModel } from '../models/contractModel'
import AppHelper from '../helpers/appHelper'
import { Types } from 'mongoose';


export default class ContractDal {

  public appHelper: any;
  public packageHelper: any;
  constructor() {
    this.appHelper = new AppHelper;
  }


  public createContract = async (data: any): Promise<any> => {
    try {
      let model = new contractModel(data)
      let result = await model.save()
      if (result) {
        return { status: true, code: 200, message: "Data saved successfully", data: result._id }
      }
      return { status: false, code: 403, message: `Failed to save data`, data: result }
    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }
  }

  public updateContract = async (data: any, id: any): Promise<any> => {
    try {
      const find = { _id: id }
      const set = { $set: data }
      const options = { upsert: false, new: true }
      let result = await contractModel.findOneAndUpdate(find, set, options).exec()
      if (result) {
        return { status: true, code: 200, message: 'Data updated successfully', data: result }
      }
      return { status: false, code: 409, message: `Failed to update brand`, data: result }
    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }

  }



  public getContractById = async (id: string): Promise<any> => {
    try {
        let query:any =[{}]

              // Calculate date one month before current date for nearExpiry status
      const currentDate = new Date();
      const oneMonthFromNow = new Date();
      oneMonthFromNow.setMonth(currentDate.getMonth() + 1);

        query.push({_id: new Types.ObjectId(id)})
      let result: any = await contractModel.aggregate()
      .match({$and: query})
      .lookup({
        from: 'items',
        localField: 'itemId',
        foreignField: '_id',
        as: 'itemDetail',
      })
      .unwind({
        path: '$itemDetail',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
          from: 'users',
          localField: 'itemDetail.customerId',
          foreignField: '_id',
          as: 'customerDetail',
        })
        .unwind({
          path: '$customerDetail',
          preserveNullAndEmptyArrays: true,
        })
      .lookup({
        from: 'brands',
        localField: 'itemDetail.brandId',
        foreignField: '_id',
        as: 'brandDetail',
      })
      .unwind({
        path: '$brandDetail',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        from: 'products',
        localField: 'itemDetail.productId',
        foreignField: '_id',
        as: 'productDetail',
      })
      .unwind({
        path: '$productDetail',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        from: 'models',
        localField: 'itemDetail.modelId',
        foreignField: '_id',
        as: 'modelDetail',
      })
      .unwind({
        path: '$modelDetail',
        preserveNullAndEmptyArrays: true,
      })
       .lookup({
          from: 'quotations',
          localField: 'contractTypeId',
          foreignField: '_id',
          as: 'quotationDetail',
        })
        .unwind({
          path: '$quotationDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'contractquotations',
          localField: 'quotationDetail.quotationId',
          foreignField: '_id',
          as: 'contractquotationDetail',
        })
        .unwind({
          path: '$contractquotationDetail',
          preserveNullAndEmptyArrays: true,
        })
      .collation({ locale: "en" })
      .project({
        _id: 1,
        itemDetail: 1,
        customerDetail: 1,
        brandName: '$brandDetail.brandName',
        productName: '$productDetail.productTypeName',
        modelName: '$modelDetail.modelName',
        customerAddress: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$customerDetail.customerAddress",
                as: "address",
                cond: { $eq: ["$$address.status", true] }
              }
            },
            0
          ]
        },
        status: {
            $cond: {
              // First check if startDate is after current date (pending)
              if: { $gt: ["$startDate", currentDate] },
              then: "pending",
              else: {
                $cond: {
                  // Check if current date is after endDate (expired)
                  if: { $gt: [currentDate, "$endDate"] },
                  then: "expired",
                  else: {
                    $cond: {
                      // Check if endDate is within 1 month from now (nearExpiry)
                      if: {
                        $and: [
                          { $lte: [currentDate, "$endDate"] },
                          { $lte: ["$endDate", oneMonthFromNow] }
                        ]
                      },
                      then: "nearExpiry",
                      else: {
                        $cond: {
                          // Check if current date is between startDate and endDate (active)
                          if: {
                            $and: [
                              { $gte: [currentDate, "$startDate"] },
                              { $lte: [currentDate, "$endDate"] }
                            ]
                          },
                          then: "active",
                          else: "pending" // fallback
                        }
                      }
                    }
                  }
                }
              }
            }
        },
        startDate: 1,
        endDate: 1,
        quantity: 1,
        price: 1,
        contractType:'$contractquotationDetail.name',
      })
      .exec()
      if (result) {

        return { status: true, code: 200, message: 'Data fetched successfully', data: result }
      }
      return { status: true, code: 200, message: 'No such document!', data: [] }
    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }
  }


  public getContract = async (req: any): Promise<any> => {
    try {
      let offset = req.query.offset ? req.query.offset : 0;
      let limit = req.query.limit ? req.query.limit : 15;
      let query = [{}]
      let specialField: string = req.query.specialField; // new parameter for specialized search
      
      if (req.query.search) {

        let searchValue: any = []
        let value = req.query.search;
        let globalValue = String(value).replace(/([.*+?=^!:${}()|[\]\/\\])/g, '\\$1');

        if (specialField && req.query.type === "ticket") {
          if (specialField === 'customerName') {
            searchValue.push({ 'customerName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          } else if (specialField === 'model') {
            searchValue.push({ 'modelName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          }
          else if (specialField === 'brand') {
            searchValue.push({ 'brandName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          }
          else if (specialField === 'product') {
            searchValue.push({ 'productName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          }
          else if (specialField === 'serialNo') {
            searchValue.push({ 'serialNo': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          }
          else if (specialField === 'phone' && !isNaN(value)) {
            // Search in concatenated countryCode + number for special field
            searchValue.push({
              $expr: {
                $regexMatch: {
                  input: {
                    $concat: [
                      { $toString: "$phone.countryCode" },
                      { $toString: "$phone.number" }
                    ]
                  },
                  regex: parseInt(value).toString(),
                  options: "i"
                }
              }
            });
          }
        } else if(req.query.type === "ticket") {
          // If specialField is invalid, default to normal search behavior
          searchValue.push({ 'serialNo': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          searchValue.push({ 'customerName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          searchValue.push({ 'brandName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          searchValue.push({ 'modelName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          searchValue.push({ 'productName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          if (!isNaN(value)) {
            // Search in concatenated countryCode + number
            searchValue.push({
              $expr: {
                $regexMatch: {
                  input: {
                    $concat: [
                      { $toString: "$phone.countryCode" },
                      { $toString: "$phone.number" }
                    ]
                  },
                  regex: parseInt(value).toString(),
                  options: "i"
                }
              }
            });

            // Also search in number field only (fallback)
            searchValue.push({
              $expr: {
                $regexMatch: {
                  input: { $toString: "$phone.number" },
                  regex: "^" + parseInt(value),
                  options: "i"
                }
              }
            });
          }
        }
        else {
          searchValue.push({ 'serialNo': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          searchValue.push({ 'customerName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          if (!isNaN(value)) {
            // Search in concatenated countryCode + number
            searchValue.push({
              $expr: {
                $regexMatch: {
                  input: {
                    $concat: [
                      { $toString: "$phone.countryCode" },
                      { $toString: "$phone.number" }
                    ]
                  },
                  regex: parseInt(value).toString(),
                  options: "i"
                }
              }
            });

            // Also search in number field only (fallback)
            searchValue.push({
              $expr: {
                $regexMatch: {
                  input: { $toString: "$phone.number" },
                  regex: "^" + parseInt(value),
                  options: "i"
                }
              }
            });
          }
        }


        query.push({ $or: searchValue });

      }

      if (req.query.status) {
        query.push({
          status: req.query.status
        })
      }

      if (req.query.contractType) {
        query.push({
          contractTypeId: new Types.ObjectId(req.query.contractType)
        })
      }

      // Calculate date one month before current date for nearExpiry status
      const currentDate = new Date();
      const oneMonthFromNow = new Date();
      oneMonthFromNow.setMonth(currentDate.getMonth() + 1);

      let result: any = await contractModel.aggregate()
        .lookup({
          from: 'items',
          localField: 'itemId',
          foreignField: '_id',
          as: 'itemDetail',
        })
        .unwind({
          path: '$itemDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'users',
          localField: 'itemDetail.customerId',
          foreignField: '_id',
          as: 'customerDetail',
        })
        .unwind({
          path: '$customerDetail',
          preserveNullAndEmptyArrays: true,
        })
        // Add this stage to extract the active customer address
        .addFields({
          customerAddress: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$customerDetail.customerAddress',
                  cond: { $eq: ['$$this.status', true] }
                }
              },
              0
            ]
          }
        })
        .lookup({
          from: 'quotations',
          localField: 'contractTypeId',
          foreignField: '_id',
          as: 'quotationDetail',
        })
        .unwind({
          path: '$quotationDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'contractquotations',
          localField: 'quotationDetail.quotationId',
          foreignField: '_id',
          as: 'contractquotationDetail',
        })
        .unwind({
          path: '$contractquotationDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'brands',
          localField: 'itemDetail.brandId',
          foreignField: '_id',
          as: 'brandDetail',
        })
        .unwind({
          path: '$brandDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'products',
          localField: 'itemDetail.productId',
          foreignField: '_id',
          as: 'productDetail',
        })
        .unwind({
          path: '$productDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'models',
          localField: 'itemDetail.modelId',
          foreignField: '_id',
          as: 'modelDetail',
        })
        .unwind({
          path: '$modelDetail',
          preserveNullAndEmptyArrays: true,
        })
        .collation({ locale: "en" })
        .project({
          _id: 1,
          serialNo: '$itemDetail.serialNo',
          customerName: '$customerDetail.fullName',
          phone: '$customerDetail.phone',
          email: '$customerDetail.email',
          telePhoneNo: '$customerDetail.alternatePhone',
          address: "$itemDetail.installationAddress",
          customerAddress: 1,
          assignmentDetail: '$itemDetail.assignmentDetail',
          installationDate: '$itemDetail.installationDate',
          warrantyStartDate: '$itemDetail.warrantyStartDate',
          warrantyEndDate: '$itemDetail.warrantyEndDate',
          // Dynamic status calculation
          status: {
            $cond: {
              // First check if startDate is after current date (pending)
              if: { $gt: ["$startDate", currentDate] },
              then: "pending",
              else: {
                $cond: {
                  // Check if current date is after endDate (expired)
                  if: { $gt: [currentDate, "$endDate"] },
                  then: "expired",
                  else: {
                    $cond: {
                      // Check if endDate is within 1 month from now (nearExpiry)
                      if: {
                        $and: [
                          { $lte: [currentDate, "$endDate"] },
                          { $lte: ["$endDate", oneMonthFromNow] }
                        ]
                      },
                      then: "nearExpiry",
                      else: {
                        $cond: {
                          // Check if current date is between startDate and endDate (active)
                          if: {
                            $and: [
                              { $gte: [currentDate, "$startDate"] },
                              { $lte: [currentDate, "$endDate"] }
                            ]
                          },
                          then: "active",
                          else: "pending" // fallback
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          startDate: 1,
          endDate: 1,
          quantity: 1,
          price: 1,
          contractType: '$contractquotationDetail.name',
          contractTypeId: 1,
          brandName: '$brandDetail.brandName',
          productName: '$productDetail.productTypeName',
          modelName: '$modelDetail.modelName',
        })
        .match({ $and: query })
        .skip(parseInt(offset))
        .limit(parseInt(limit))
        .exec();

      let count: any = await contractModel.aggregate()
        .lookup({
          from: 'items',
          localField: 'itemId',
          foreignField: '_id',
          as: 'itemDetail',
        })
        .unwind({
          path: '$itemDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'users',
          localField: 'itemDetail.customerId',
          foreignField: '_id',
          as: 'customerDetail',
        })
        .unwind({
          path: '$customerDetail',
          preserveNullAndEmptyArrays: true,
        })
        // Add this stage to extract the active customer address
        .addFields({
          customerAddress: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$customerDetail.customerAddress',
                  cond: { $eq: ['$$this.status', true] }
                }
              },
              0
            ]
          }
        })
        .lookup({
          from: 'quotations',
          localField: 'contractTypeId',
          foreignField: '_id',
          as: 'quotationDetail',
        })
        .unwind({
          path: '$quotationDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'contractquotations',
          localField: 'quotationDetail.quotationId',
          foreignField: '_id',
          as: 'contractquotationDetail',
        })
        .unwind({
          path: '$contractquotationDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'brands',
          localField: 'itemDetail.brandId',
          foreignField: '_id',
          as: 'brandDetail',
        })
        .unwind({
          path: '$brandDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'products',
          localField: 'itemDetail.productId',
          foreignField: '_id',
          as: 'productDetail',
        })
        .unwind({
          path: '$productDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'models',
          localField: 'itemDetail.modelId',
          foreignField: '_id',
          as: 'modelDetail',
        })
        .unwind({
          path: '$modelDetail',
          preserveNullAndEmptyArrays: true,
        })
        .collation({ locale: "en" })
        .project({
          _id: 1,
          serialNo: '$itemDetail.serialNo',
          customerName: '$customerDetail.fullName',
          phone: '$customerDetail.phone',
          email: '$customerDetail.email',
          telePhoneNo: '$customerDetail.alternatePhone',
          address: "$itemDetail.installationAddress",
          customerAddress: 1,
          assignmentDetail: '$itemDetail.assignmentDetail',
          installationDate: '$itemDetail.installationDate',
          // Dynamic status calculation for count query
          status: {
            $cond: {
              if: { $gt: ["$startDate", currentDate] },
              then: "pending",
              else: {
                $cond: {
                  if: { $gt: [currentDate, "$endDate"] },
                  then: "expired",
                  else: {
                    $cond: {
                      if: {
                        $and: [
                          { $lte: [currentDate, "$endDate"] },
                          { $lte: ["$endDate", oneMonthFromNow] }
                        ]
                      },
                      then: "nearExpiry",
                      else: {
                        $cond: {
                          if: {
                            $and: [
                              { $gte: [currentDate, "$startDate"] },
                              { $lte: [currentDate, "$endDate"] }
                            ]
                          },
                          then: "active",
                          else: "pending"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          startDate: 1,
          endDate: 1,
          quantity: 1,
          price: 1,
          contractType: '$contractquotationDetail.name',
          contractTypeId: 1,
          brandName: '$brandDetail.brandName',
          productName: '$productDetail.productTypeName',
          modelName: '$modelDetail.modelName',
        })
        .match({ $and: query })
        .count('totalCount')
        .unwind({ path: '$totalCount', preserveNullAndEmptyArrays: true })
        .exec()

      const [resultPromise] = await Promise.all([result])

      let response = {
        "data": resultPromise,
        "fetchCount": resultPromise.length,
        "totalCount": count[0] && count[0].totalCount ? count[0].totalCount : 0
      }
      return { status: true, code: 200, message: 'data Fetched successfully', data: response }

    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }
  }




}