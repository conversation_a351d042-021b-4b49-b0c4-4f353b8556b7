import ApiResponse from '../helpers/response'
import proposalDal from '../dal/proposalDal'
import mailController from './mailController';


export default class ProposalController {
  public proposalDal: proposalDal;
  public mailController: mailController;
  constructor() {
    this.proposalDal = new proposalDal()
    this.mailController = new mailController()
  }

  public createProposal = async (req: any) => {
       try {
         let reqBody: any = req.body;
         reqBody["createdById"] = req.headers.userid;
         reqBody["updatedById"] = req.headers.userid;

         const parsedQuotations = JSON.parse(reqBody.quotations);
         console.log("Parsed Quotations: ", parsedQuotations);
         // Map through quotations and set default values for price and quantity
         reqBody.quotations = parsedQuotations.map((quote: any) => ({
           contractTypeId: quote.contractTypeId,
           price: !quote.price || quote.price === "" ? 0 : Number(quote.price),
           quantity: !quote.quantity || quote.quantity === "" ? 0 : Number(quote.quantity),
           details: quote.details || ""
         }));

         let result = await this.proposalDal.createProposal(reqBody);
         if (!result.status) {
           return new ApiResponse(result.code, false, result.message, result.data);
         }
         if (reqBody.status === "sent") {
          if(!req.files || req.files.length === 0) {
            return new ApiResponse(400, false, "Please attach files", null);
          }

          if (!reqBody.email) {
            return new ApiResponse(400, false, "Please provide email", null);
          }
           let mail = await this.mailController.sendMail(req)
           if (!mail.status) {
             return new ApiResponse(mail.code, false, mail.message, mail.data);
           }
         }

         return new ApiResponse(200, true, 'Item created successfully', result.data);
    } catch (error: any) {
      return new ApiResponse(500, false, 'Error creating proposal', error.message);
    }
  };

 

  public fetchProposal = async (req: any) => {
    try {
      let result = await this.proposalDal.getProposal(req)
      if (!result.status) {
        return new ApiResponse(result.code, false, result.message, result.data);
      }
      return new ApiResponse(result.code, true, result.message, result.data);
    } catch (error: any) {
      return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
    }

  }



  public fetchProposalById = async (req: any) => {
    try {
      let id: string = req.params.proposalId
      let type: string = req.query.type
      let result = await this.proposalDal.getProposalById(id,type)
      if (!result.status) {
        return new ApiResponse(result.code, false, result.message, result.data);
      }
      return new ApiResponse(result.code, true, result.message, result.data);
    } catch (error: any) {
      return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
    }
  }





  public updateProposalById = async (req: any) => {
    try {
      let id: string = req.params.proposalId;
      req.body["updatedById"] = req.headers.userid;

      let result = await this.proposalDal.updateProposal(req.body, id)
      if (!result.status) {
        return new ApiResponse(result.code, false, result.message, result.data);
      }

      return new ApiResponse(result.code, true, result.message, result.data);

    } catch (error: any) {
      return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
    }

  }

  

}
