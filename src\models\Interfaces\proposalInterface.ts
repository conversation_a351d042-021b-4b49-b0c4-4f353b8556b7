import { Document, Types } from 'mongoose';

export interface IProposalModel extends Document {
    _id: Types.ObjectId;
     brandId: Types.ObjectId;
     productId: Types.ObjectId;
     serialNo: string;
     customerName: string;
         contactPerson: string;
         email: string;
         phone: {
           countryCode: string,
           number: string
         };
         telePhoneNo: string;
         customerAddress: {};
         installationAddress: {};
         assignmentDetail: object;
         installationDate: Date;
         purchaseDate: Date;
         warrantyStartDate: Date;
         warrantyEndDate: Date;
         status: string,
     createdById: Types.ObjectId;
     updatedById: Types.ObjectId
  
}
