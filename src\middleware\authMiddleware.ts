import { Request, Response, NextFunction } from 'express'
import AuthService from '../services/authenticationService';
import * as jwt from 'jsonwebtoken';


const authService = new AuthService()

const AuthMiddleWare = async (req: Request, res: Response, next: NextFunction) => {
  try {
    global.logger.silly(`Method: ${req.method}, Path: ${req.originalUrl}`)
    if (
      req.originalUrl == '/brand'
      ) {
      return next()
    }
    console.log("req.headers",req.headers)

  //  if (!req.headers.userid || (req.headers.userid == "")) {
  //     return res.status(401).send({ code:401, status: false, message: 'Please Check userid', data: [] })
  //   }
  //   const authorization: string = req.headers.authorization ? req.headers.authorization : ''
  //   if (!authorization) {
  //     return res.status(401).send({ code:401, status: false, message: 'Token missing in headers', data: [] })
  //   }
  //   const tokenDecoded: any = jwt.decode(authorization)
  //   if (!tokenDecoded) {
  //     return res.status(401).send({ code:401, status: false, message: 'Authentication error-Token Decoding Failed', data: tokenDecoded })
  //   }
  //   const tokenUserId = tokenDecoded.userId;
  //   if (!tokenUserId) {
  //     return res.status(401).send({ code:401, status: false, message: 'User data missing in token', data: [] })
  //   }
  //   let userId: any = req.headers.userid;
  //   if (userId != tokenUserId) {
  //     return res.status(401).send({ code:401, status: false, message: 'Invalid user', data: [] })
  //   }
  //   let result = await authDal.getAuthUserByUserId(userId)
  //   if (!result.status) {
  //     return res.status(401).send({ code:401, status: false, message: 'User not found', data: [] })
  //   }
  //   if (!result.data.secret) {
  //     return res.status(401).send({ code:401, status: false, message: 'User data missing in token', data: [] })
  //   }
  //   let verify: any = jwt.verify(authorization, result.data.secret)
  //   if (!verify) {
  //     return res.status(403).send({ code: 403, status: false, message: "Unauthorized", data: verify });
  //   }
    next()
  } catch (error: any) {
    console.log("error", error)
    return res.status(500).send({code:500, status: false, message: 'Internal-Server-Error', data: error.message ? error.message : "Internal server error" })
  }
}

export default AuthMiddleWare
