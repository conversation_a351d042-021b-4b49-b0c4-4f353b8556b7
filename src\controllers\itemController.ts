import ApiResponse from '../helpers/response'
import itemDal from '../dal/itemDal'



export default class ItemController {
  public itemDal: itemDal;
  constructor() {
    this.itemDal = new itemDal()

  }

  public createItem = async (req: any) => {
    try {
      let reqBody: any = req.body
      reqBody["createdById"] = req.headers.userid;
      reqBody["updatedById"] = req.headers.userid;
      let result = await this.itemDal.itemCreation(reqBody)
      if (!result.status) {
        return new ApiResponse(result.code, false, result.message, result.data);
      }
      return new ApiResponse(200, true, 'Item created successfully', result.data);
    } catch (error: any) {
      return new ApiResponse(500, false, 'Error creating  item', error.message);
    }
  };

 

  public fetchItem = async (req: any) => {
    try {
      let result = await this.itemDal.getItem(req)
      if (!result.status) {
        return new ApiResponse(result.code, false, result.message, result.data);
      }
      return new ApiResponse(result.code, true, result.message, result.data);
    } catch (error: any) {
      return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
    }

  }



  public fetchItemById = async (req: any) => {
    try {
      let id: string = req.params.itemId
      let result = await this.itemDal.getItemById(id)
      if (!result.status) {
        return new ApiResponse(result.code, false, result.message, result.data);
      }
      return new ApiResponse(result.code, true, result.message, result.data);
    } catch (error: any) {
      return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
    }
  }





  public updateItemById = async (req: any) => {
    try {
      let id: string = req.params.itemId;
      req.body["updatedById"] = req.headers.userid;

      let result = await this.itemDal.updateItem(req.body, id)
      if (!result.status) {
        return new ApiResponse(result.code, false, result.message, result.data);
      }

      return new ApiResponse(result.code, true, result.message, result.data);

    } catch (error: any) {
      return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
    }

  }

  

}
