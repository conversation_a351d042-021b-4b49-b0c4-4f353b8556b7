export default {
  domain: process.env.DOMAIN ? process.env.DOMAIN : "",
  app: {
    port: process.env.APP_PORT ? parseInt(process.env.APP_PORT, 10) : 4000,
  },
  dataBase: {
    url: process.env.MONGODB_URL ? process.env.MONGODB_URL : ""
  },
  ovh:{
    ACCESSKEYID: process.env.ACCESSKEYID ? process.env.ACCESSKEYID : "",
    SECRETACCESSKEY: process.env.SECRETACCESSKEY ? process.env.SECRETACCESSKEY : "",
    BUCKET_NAME: process.env.BUCKET_NAME ? process.env.BUCKET_NAME : "",
    ENDPOINT: process.env.ENDPOINT ? process.env.ENDPOINT : "",
  },
  email: {
    host: process.env.EMAIL_HOST ? process.env.EMAIL_HOST : "",
    port: process.env.PORT ? process.env.PORT : 465,
    username: process.env.EMAIL_ID ? process.env.EMAIL_ID : "", 
    password: process.env.EMAIL_PWD ? process.env.EMAIL_PWD : ""
  },
  
  forgotpassword: {
    link: process.env.FORGOTPASSWORD_LINK ? process.env.FORGOTPASSWORD_LINK :"",
   
  },
  services: {
    authentication: {
      baseUrl: process.env.AUTHENTICATION_URL ? process.env.AUTHENTICATION_URL : ""
    },
    space: {
      baseUrl: process.env.SPACE_URL ? process.env.SPACE_URL : ""
    }

  }
}
