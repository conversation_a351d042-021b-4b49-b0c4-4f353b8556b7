import { Schema, Model, model } from "mongoose";
import { IContractModel } from "./Interfaces/contractInterface";

const contractSchema = new Schema(
  {
    itemId: { type: Schema.Types.ObjectId },
    contractTypeId: { type: Schema.Types.ObjectId },
    price: { type: Number },
    quantity: { type: Number },
    paymentTerms: { type: String },
    preventiveMaintenance: { type: String },
    installationDate :{type: Date },
    startDate: { type: Date },
    endDate: { type: Date },
    createdById: { type: Schema.Types.ObjectId },
    updatedById: { type: Schema.Types.ObjectId }
  },
  {
    timestamps: true,
  }
);

export const contractModel: Model<IContractModel> = model<IContractModel>(
  "contract",
  contractSchema
);
