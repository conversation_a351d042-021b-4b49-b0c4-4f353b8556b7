import * as express from 'express';
import * as multer from 'multer';
import Mail from '../controllers/mailController';
import ApiResponse from '../helpers/response';

class MailRoute {
    public path = '/';
    public router = express.Router();
    private mailController: Mail;
    private upload = multer();

    constructor() {
        this.initRoutes();
        this.mailController = new Mail();
    }

    public initRoutes() {
        this.router.route('/mail')
            .post(this.upload.array('attachments'), async (req, res) => {
                console.log('mail route');
                const resBody: ApiResponse = await this.mailController.sendMail(req);
                return res.status(resBody.code).send(resBody);
            });
    }
}

export default MailRoute;