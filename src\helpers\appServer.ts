import express = require('express')
import { Application } from 'express'
import Logger from '../helpers/logger'
import * as appRootPath from 'app-root-path'

declare global {
  namespace NodeJS {
    interface Global {
      logger: any
      rootPath: any
    }
  }
}

class App {
  public app: Application
  public port: number

  constructor(appInit: { port: number; defaults: any; middleWares: any; routes: any }) {
    this.setGlobals()
    global.logger.info('Application Initialization Started')
    this.app = express()
    this.port = appInit.port
    this.setDefaults(appInit.defaults)
    this.middlewares(appInit.middleWares)
    this.routes(appInit.routes)
    this.assets()
    this.template()
    // this.connection()
  }
  private setGlobals() {
    global.logger = new Logger().getLogger()
    global.rootPath = appRootPath
  }
  private setDefaults(middleWares: { forEach: (arg0: (middleWare: any) => void) => void }) {
    global.logger.info('Application Setting Defaults')
    middleWares.forEach((middleWare) => {
      this.app.use(middleWare)
    })
  }
  private middlewares(middleWares: { forEach: (arg0: (middleWare: any) => void) => void }) {
    global.logger.info('Application Setting Middlewares')
    middleWares.forEach((middleWare) => {
      this.app.use(middleWare)
    })
  }

  private routes(controllers: { forEach: (arg0: (controller: any) => void) => void }) {
    global.logger.info('Application Setting Routes')
    controllers.forEach((controller) => {
      this.app.use('/', controller.router)
    })
  }
  private assets() {
    this.app.use(express.static('public'))
    this.app.use(express.static('views'))
  }

  private template() {
    this.app.set('view engine', 'jade')
  }

  // public connection = async () => {
  // await dbConnection()
  // }

  public listen() {
    this.app.listen(this.port, () => {
      global.logger.info(`Application listening on the port :${this.port}`)
    })
  }
}
export default App
