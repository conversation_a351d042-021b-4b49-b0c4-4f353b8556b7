import config from '../config/appConfig'
import * as nodeMailer from 'nodemailer'


export default class MailHelper {
  public sendEmail = async (name: string, data: any,password: string) => {
    try {
    const Sib = require('sib-api-v3-sdk');
    const client = Sib.ApiClient.instance;
    const apiKey = client.authentications['api-key'];
    // apiKey.apiKey = config.sendMail.apiKey;
    

    const tranEmailApi = new Sib.TransactionalEmailsApi();
      const sender = {
        // email: config.sendMail.senderEmail,
        // name: config.sendMail.senderName,
      };
      const receivers = [
      {
        email: data.email,
      },
      
      
    ];
     
      const mailContent = `
          <html>
            <head>
              <style>
                h1 {
                  color: #333;
                }
                p {
                  font-size: 16px;
                  line-height: 1.5;
                }
              </style>
            </head>
            <body>
              <h1>Welcome</h1>
              <p>Hello ${name},</p>
              <p>You have successfully registered.</p>
              <p><b>Email:</b> ${data.email}</p>
              <p><b>Password:</b> ${password}</p>
              <p>Login link: <a href="${config.domain}">${config.domain}</a></p>
            </body>
          </html>
        `
      const emailParams = {
        sender,
        to: receivers,
        subject: 'Welcome mail',
        htmlContent: mailContent
        };
        const sendEmailResponse = await tranEmailApi.sendTransacEmail(emailParams);
      }catch (error: any) {
        console.log(error.message)
      return { code: 500, status: false, message: `Database Error`, data: error.message };
    }
  }
 
  public sendMail = async (email: string, data: any, attachments?: any[]) => {
    try {
        const transporter = nodeMailer.createTransport({
            host: config.email.host,
            port: config.email.port,
            secureConnection: true,
            auth: {
                user: config.email.username,
                pass: config.email.password
            },
            tls: { rejectUnauthorized: false }
        } as any);

        const mailOptions = {
            from: config.email.username,
            to: email,
            subject: data.title,
            html: data.message,
            attachments: attachments || [] // Add attachments if provided
        };

        const info = await transporter.sendMail(mailOptions);

        console.log(`Mail sent successfully ${info.response}`);
        return {status: true, message: info.response};
    }
    catch (error: any) {
        console.log(error.message);
        return { code: 500, status: false, message: `Database Error`, data: error.message };
    }
};





}