import { Types } from "mongoose";
import appHelper from '../helpers/appHelper'

export default class leadHelper {

  public appHelper: any;
  constructor() {
    this.appHelper = new appHelper();
  }

  public sorting = async (reqQuery: any) => {
    try {
      let sort: any = reqQuery.sort ? reqQuery.sort : "";
      switch (sort) {
        case 'updatedAtAsc':
          sort = 'updatedAt'
          break;
        case 'updatedAtDesc':
          sort = 'updatedAt'
          break;
        case 'emailDesc':
          sort = '-email'
          break;
        case 'emailAsc':
          sort = 'email'
          break;
        case 'phoneAsc':
          sort = 'phone'
          break;
        case 'phoneDesc':
          sort = '-phone'
          break;
        default:
          sort = '-updatedAt'
          break;
      }

      return sort;
    } catch (error: any) {
      return { status: false, code: 409, message: 'Something went wrong', data: error.message }
    }

  }

  public filter = async (reqQuery: any) => {
    try {
      let queryArray: any = [{}];
      if (reqQuery.status) {
        (reqQuery.status).trim()
        if (reqQuery.status == 'false') {
          queryArray.push({ userStatus: false })
        }
        if (reqQuery.status == 'true') {
          queryArray.push({ userStatus: true })
        }
      }
     
    
      if (reqQuery.updated) {
        let updatedTimeArray = reqQuery.updated.split('-');
        queryArray.push({ updatedAt: { $gte: new Date(parseInt(updatedTimeArray[0])), $lte: new Date(parseInt(updatedTimeArray[1])) } })
      }
      if (reqQuery.created) {
        let createdTimeArray = reqQuery.created.split('-');
        queryArray.push({ createdAt: { $gte: new Date(parseInt(createdTimeArray[0])), $lte: new Date(parseInt(createdTimeArray[1])) } })
      }
      if (reqQuery.value) {
        let value = (reqQuery.value).trim();
        const emailRegex: any = new RegExp('^[^@]*' + reqQuery.value);
        let searchQuery = {
          $or: [
            { $expr: { $regexMatch: { input: { $concat: ["$name.first", " ", { $ifNull: ["$name.middle", "$name.last"] }, " ", "$name.last"] }, regex: new RegExp(value), options: "im" } } },
            { numberString: { $regex: new RegExp(value.replace(/[`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi, '')), $options: "im" } },
            { email: { $regex: new RegExp(emailRegex), $options: "im" } }
          ]
        }
        queryArray.push(searchQuery)
      }
      return queryArray;
    } catch (error: any) {
      return { status: false, code: 409, message: 'Something wrong while adding filter for employee', data: error.message }
    }
  }



  

 




}
















