import ApiResponse from '../helpers/response'
import contractDal from '../dal/contractDal'



export default class ContractController {
  public contractDal: contractDal;
  constructor() {
    this.contractDal = new contractDal()

  }

  public createContract = async (req: any) => {
    try {
      let reqBody: any = req.body
      reqBody["createdById"] = req.headers.userid;
      reqBody["updatedById"] = req.headers.userid;
      let result = await this.contractDal.createContract(reqBody)
      if (!result.status) {
        return new ApiResponse(result.code, false, result.message, result.data);
      }
      return new ApiResponse(200, true, 'Item created successfully', result.data);
    } catch (error: any) {
      return new ApiResponse(500, false, 'Error creating contract', error.message);
    }
  };

 

  public fetchContract = async (req: any) => {
    try {
      let result = await this.contractDal.getContract(req)
      if (!result.status) {
        return new ApiResponse(result.code, false, result.message, result.data);
      }
      return new ApiResponse(result.code, true, result.message, result.data);
    } catch (error: any) {
      return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
    }

  }



  public fetchContractById = async (req: any) => {
    try {
      let id: string = req.params.contractId
      let result = await this.contractDal.getContractById(id)
      if (!result.status) {
        return new ApiResponse(result.code, false, result.message, result.data);
      }
      return new ApiResponse(result.code, true, result.message, result.data);
    } catch (error: any) {
      return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
    }
  }





  public updateContractById = async (req: any) => {
    try {
      let id: string = req.params.contractId;
      req.body["updatedById"] = req.headers.userid;

      let result = await this.contractDal.updateContract(req.body, id)
      if (!result.status) {
        return new ApiResponse(result.code, false, result.message, result.data);
      }

      return new ApiResponse(result.code, true, result.message, result.data);

    } catch (error: any) {
      return new ApiResponse(500, false, 'internal-server-error', `${error.message}`);
    }

  }

  

}
