import { Request, Response } from 'express';
import  Mail<PERSON><PERSON>per  from '../helpers/sendMailHelper'
import ApiResponse from '../helpers/response';
import Logger from '../helpers/logger'

declare global {
  namespace NodeJS {
    interface Global {
      logger: any
      gcpBucket: any
    }
  }
}

 export default class  mailController {

   private mailHelper: MailHelper
   constructor(
     mail = new MailHelper()
   ) {
     this.setGlobals()
     this.mailHelper = mail
   }


  private setGlobals() {
    global.logger = new Logger().getLogger()
    global.gcpBucket = ""
  }

  public sendMail = async (req: Request) => {
    try {
        if (!req.body) {
            return new ApiResponse(400, false, "Please pass payload", null);
        }
      
        const email = req.body.email;
        const files :any= req.files; // Assuming you're using multer or similar middleware

        let data = {
            title: "Proposal Details",
            message: "Please find the attached proposal details",
        }

        if (!email) {
            return new ApiResponse(400, false, "Please provide email fields", null);
        }



        // Prepare attachments array
        const attachments :any = files ? Object.keys(files).map((key: any) => {
            const file = files[key];
            return {
                filename: file.originalname,
                content: file.buffer
            };
        }) : [];

        const result = await this.mailHelper.sendMail(email, data, attachments);
        console.log("result : =} ", result);
        if (result.status) {
            global.logger.info(`Mail sent successfully : ${result.toString()}`);
            return new ApiResponse(200, true, "Mail sent successfully", "Mail sent successfully");
        }

        global.logger.error(`Mail not sent: ${JSON.stringify(result)}`);
        return new ApiResponse(400, false, "Failed to send mail", result.message);
    } catch (error: any) {
        global.logger.error(`error @catch block : ${JSON.stringify(error)}`);
        return new ApiResponse(500, false, error.message ? error.message : "Internal server error");
    }
}

  // public sendBulkMail = async (req: Request, res: Response) => {
  //   try {
  //     if (!req.body) {
  //       return new ApiResponse(400, false, "Please pass payload",res);
  //     }

  //     const subject = req.body.subject;
  //     const body = req.body.content;
  //     const rejected: string[] = [];

  //     for (const iterator of req.body.email) {
  //       const result = await this.mailHelper.sendMail(subject, iterator, body)

  //       if (!result.status) {
  //         rejected.push(iterator);
  //       }
  //     }

  //     if (rejected.length > 0) {
  //       return new ApiResponse(400, false, "Failed to send mail", rejected);
  //     }

  //     return new ApiResponse(200, true, "Mail sent successfully", "Mail sent successfully");
  //   } catch (error:any) {
  //     global.logger.error(`error @catch block : ${JSON.stringify(error)}`)
  //     return new ApiResponse( 500, false, error.message ? error.message : "Internal server error");
  //   }
  // }
};