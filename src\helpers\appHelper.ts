
import config from '../config/appConfig'
export default class AppHelper {

  static saveDataToCsv: any;

  

  public checkTestingDomain = (domain: string) => {
    console.log(domain)
    if (domain === "authentication:5000" || domain === "localhost:3000") {
      return true;
    }
    return false;
  }


  public randomString = (length: number) => {
    let result = '';
    let characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }
 
 
  public OTPGenerate = async () => {
    const min = 10000;
    const max = 99999;
    const random = (Math.floor(100000) + Math.random() * 900000).toString().substr(0, 6);
    return parseInt(random);
  };
  
 
}